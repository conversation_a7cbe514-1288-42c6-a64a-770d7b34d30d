# 消息侧边栏功能

## 功能概述

实现了一个侧边栏功能，可以在 LLM 的返回消息组件中点击按钮展开，展开时将该条消息的原始数据带入并进行显示。

## 功能特性

### 显示内容分为5个区块：

1. **风格标签** - 以横向 tag 列表展示，可滑动，蓝色主题
2. **品牌标签** - 以横向 tag 列表展示，可滑动，绿色主题  
3. **款式标签** - 以横向 tag 列表展示，可滑动，紫色主题
4. **简要概括** - 包含一条或多条一句话描述，以 card 展示，纵向排列，橙色边框
5. **具体描述** - 包含一条或多条一句话描述，以 card 展示，纵向排列，靛蓝色边框

### 交互特性

- 只有 AI 助手的消息才会显示侧边栏按钮
- 只有包含有效原始数据的消息才会显示按钮
- 点击消息右上角的信息图标（Info）打开侧边栏
- 侧边栏从右侧滑入，支持关闭操作
- 响应式设计，在不同屏幕尺寸下自适应

## 技术实现

### 文件结构

```
src/
├── types/
│   └── messageData.ts          # 消息数据类型定义
├── components/chat/
│   ├── ChatMessage.tsx         # 修改后的消息组件
│   ├── MessageSidebar.tsx      # 新增的侧边栏组件
│   └── SidebarTest.tsx         # 测试组件
└── ui/components/ui/
    └── sheet.tsx               # Radix UI Sheet 组件
```

### 核心组件

#### MessageSidebar
- 使用 Radix UI Sheet 组件实现侧边栏
- 支持响应式宽度（400px / 540px）
- 分区域展示不同类型的数据

#### ChatMessage (修改)
- 添加了侧边栏按钮
- 集成原始数据提取逻辑
- 条件渲染侧边栏按钮

### 数据流

1. **数据提取**: 在 `useChat` hook 中，从 LLM 响应中提取原始 JSON 数据
2. **数据存储**: 将原始数据存储在 `ChatMessage.rawData` 字段中
3. **数据转换**: 使用 `transformToSidebarData` 函数将原始数据转换为侧边栏显示格式
4. **数据展示**: 在侧边栏中按区块展示转换后的数据

### 类型定义

```typescript
// 原始数据结构（来自 LLM）
interface MessageRawData {
  debug: {
    keyword: {
      style: string[]
      type: string[]
      brand: string[]
    }
  }
  output: {
    bioSubjective: string
    bioObjective: string
    descriptionSocial: string
    descriptionFriend: string
    descriptionMagazine: string
    descriptionNeutral: string
  }
}

// 侧边栏显示数据
interface SidebarData {
  styleTags: string[]
  brandTags: string[]
  typeTags: string[]
  briefDescriptions: string[]
  detailedDescriptions: string[]
}
```

## 使用方法

### 在实际聊天中使用

1. 启动应用并进入聊天界面
2. 发送消息给 AI 助手
3. 当 AI 返回包含结构化数据的响应时，消息右上角会出现信息图标
4. 点击信息图标打开侧边栏查看详细信息

### 测试功能

1. 访问应用主页
2. 点击"侧边栏测试"标签
3. 查看模拟的消息和侧边栏功能

## 样式设计

- 使用 Tailwind CSS 进行样式设计
- 支持深色模式
- 标签使用不同颜色区分类型：
  - 风格标签：蓝色 (blue-100/blue-800)
  - 品牌标签：绿色 (green-100/green-800)
  - 款式标签：紫色 (purple-100/purple-800)
- 卡片使用左边框区分类型：
  - 简要概括：橙色边框
  - 具体描述：靛蓝色边框

## 扩展性

该功能设计具有良好的扩展性：

1. **数据结构扩展**: 可以轻松添加新的数据字段和显示区块
2. **样式定制**: 可以通过修改 CSS 类来调整外观
3. **交互增强**: 可以添加更多交互功能，如标签点击、数据导出等
4. **布局调整**: 可以调整侧边栏位置、大小和动画效果

## 注意事项

- 侧边栏功能依赖于 LLM 返回特定格式的 JSON 数据
- 如果消息不包含有效的原始数据，侧边栏按钮不会显示
- 建议在生产环境中添加错误处理和加载状态
