// 消息原始数据结构，基于 LLM 返回的 JSON 格式
export interface MessageRawData {
  debug: {
    query: {
      total: number
      valid: number
      topQuery: string[]
    }
    product: {
      totalProduct: number
      totalBrand: number
      topType: string[]
      topBrand: string[]
    }
    keyword: {
      style: string[]
      type: string[]
      brand: string[]
    }
  }
  output: {
    bioSubjective: string
    bioObjective: string
    descriptionSocial: string
    descriptionFriend: string
    descriptionMagazine: string
    descriptionNeutral: string
  }
}

// 侧边栏显示的数据结构
export interface SidebarData {
  styleTags: string[]
  brandTags: string[]
  typeTags: string[]
  briefDescriptions: string[]
  detailedDescriptions: string[]
}

// 从原始数据转换为侧边栏数据的函数
export function transformToSidebarData(rawData: MessageRawData): SidebarData {
  return {
    styleTags: rawData.debug.keyword.style,
    brandTags: rawData.debug.keyword.brand,
    typeTags: rawData.debug.keyword.type,
    briefDescriptions: [
      rawData.output.bioSubjective,
      rawData.output.bioObjective,
    ].filter(Boolean),
    detailedDescriptions: [
      rawData.output.descriptionSocial,
      rawData.output.descriptionFriend,
      rawData.output.descriptionMagazine,
      rawData.output.descriptionNeutral,
    ].filter(Boolean),
  }
}
