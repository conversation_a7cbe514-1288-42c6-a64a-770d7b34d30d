import { MessagePreprocessor, ResponsePostprocessor } from "@/types/chat"

import { inputProcesser, outputProcesser } from "./preProcesser"

/**
 * Default message preprocessor - returns message as is
 */
export const defaultMessagePreprocessor: MessagePreprocessor = async (
  message: string,
): Promise<string> => {
  try {
    return inputProcesser(message)
  } catch (error) {
    console.warn("Failed to handle input", error)
    return message
  }
}

/**
 * Default response postprocessor - extracts JSON output field if present
 */
export const defaultResponsePostprocessor: ResponsePostprocessor = async (
  response: string,
): Promise<string> => {
  try {
    return await outputProcesser(response)
  } catch (error) {
    // If JSON parsing fails, return original response
    console.warn("Failed to parse JSON from response:", error)
    return response
  }
}

/**
 * Custom preprocessor that trims whitespace and converts to lowercase
 */
export const trimAndLowercasePreprocessor: MessagePreprocessor = async (
  message: string,
): Promise<string> => {
  return message.trim().toLowerCase()
}

/**
 * Custom preprocessor that adds context prefix
 */
export const addContextPreprocessor: MessagePreprocessor = async (
  message: string,
): Promise<string> => {
  return `Context: Please provide a helpful response.\n\nUser: ${message}`
}

/**
 * Custom postprocessor that removes markdown formatting
 */
export const removeMarkdownPostprocessor: ResponsePostprocessor = async (
  response: string,
): Promise<string> => {
  return response
    .replace(/\*\*(.*?)\*\*/g, "$1") // Remove bold
    .replace(/\*(.*?)\*/g, "$1") // Remove italic
    .replace(/`(.*?)`/g, "$1") // Remove inline code
    .replace(/```[\s\S]*?```/g, "") // Remove code blocks
}

/**
 * Registry of available processors
 */
export const MESSAGE_PROCESSORS = {
  preprocessors: {
    default: defaultMessagePreprocessor,
    trimAndLowercase: trimAndLowercasePreprocessor,
    addContext: addContextPreprocessor,
  },
  postprocessors: {
    default: defaultResponsePostprocessor,
    removeMarkdown: removeMarkdownPostprocessor,
  },
} as const

export type PreprocessorKey = keyof typeof MESSAGE_PROCESSORS.preprocessors
export type PostprocessorKey = keyof typeof MESSAGE_PROCESSORS.postprocessors
