"use client"

import { <PERSON><PERSON>, Info, User } from "lucide-react"
import { useState } from "react"

import { ChatMessage as ChatMessageType } from "@/types/chat"
import {
  MessageRawData,
  SidebarData,
  transformToSidebarData,
} from "@/types/messageData"
import { But<PERSON> } from "@/ui/components/ui/button"
import { cn } from "@/ui/lib/utils"

import { MessageSidebar } from "./MessageSidebar"

interface ChatMessageProps {
  message: ChatMessageType
  className?: string
}

export function ChatMessage({ message, className }: ChatMessageProps) {
  const isUser = message.role === "user"
  const isAssistant = message.role === "assistant"
  const [sidebarOpen, setSidebarOpen] = useState(false)

  // 从消息的 rawData 中获取侧边栏数据
  const getSidebarData = (): SidebarData | null => {
    if (!isAssistant || !message.rawData) return null

    try {
      return transformToSidebarData(message.rawData as MessageRawData)
    } catch (error) {
      console.warn("Failed to transform raw data:", error)
      return null
    }
  }

  const sidebarData = getSidebarData()
  const showSidebarButton = isAssistant && sidebarData

  return (
    <div
      className={cn(
        "flex w-full gap-3 px-4 py-6",
        isUser ? "bg-background" : "bg-muted/50",
        className,
      )}
    >
      {/* Avatar */}
      <div
        className={cn(
          "flex h-8 w-8 shrink-0 items-center justify-center rounded-md border shadow select-none",
          isUser ? "bg-background" : "bg-primary text-primary-foreground",
        )}
      >
        {isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
      </div>

      {/* Message Content */}
      <div className="flex-1 space-y-2 overflow-hidden">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm font-semibold">
              {isUser ? "You" : "Assistant"}
            </span>
            <span className="text-muted-foreground text-xs">
              {message.timestamp.toLocaleTimeString()}
            </span>
          </div>

          {/* 侧边栏按钮 */}
          {showSidebarButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(true)}
              className="hover:bg-muted h-6 w-6 p-0"
            >
              <Info className="h-3 w-3" />
              <span className="sr-only">查看详情</span>
            </Button>
          )}
        </div>

        <div className="prose prose-sm dark:prose-invert max-w-none">
          <div className="break-words whitespace-pre-wrap">
            {message.content}
          </div>
        </div>
      </div>

      {/* 侧边栏 */}
      {showSidebarButton && (
        <MessageSidebar
          isOpen={sidebarOpen}
          onOpenChange={setSidebarOpen}
          data={sidebarData}
        />
      )}
    </div>
  )
}
