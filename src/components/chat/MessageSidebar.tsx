"use client"

import React, { useMemo } from "react"

import { SidebarData } from "@/types/messageData"
import {
  Sheet,
  <PERSON>etContent,
  SheetHeader,
  SheetTitle,
} from "@/ui/components/ui/sheet"
import { cn } from "@/ui/lib/utils"

/**
 * 集成版本说明：
 * - 保留你的 Sheet 触发与开合行为；
 * - 内部用拍立得骨架还原“上窄下宽”的相纸留白；
 * - 顶部挂载“弹幕标签”简化版（3 条轨道，轻量 CSS 动画）；
 * - 下方为纵向滚动列表（无 Card，使用分割线 + 粘性分组标题）。
 */
export function MessageSidebar({
  isOpen,
  onOpenChange,
  data,
  className,
}: {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  data: SidebarData | null
  className?: string
}) {
  const allTags = useMemo(() => {
    return [
      ...(data?.styleTags || []),
      ...(data?.brandTags || []),
      ...(data?.typeTags || []),
    ]
  }, [data])

  const lanes = useMemo(() => {
    const base = allTags.length ? allTags : ["无标签"]
    const repeated = [...base, ...base, ...base]
    return [
      repeated.filter((_, i) => i % 3 === 0),
      repeated.filter((_, i) => i % 3 === 1),
      repeated.filter((_, i) => i % 3 === 2),
    ]
  }, [allTags])
  if (!data) return null

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetHeader>
        <SheetTitle />
      </SheetHeader>
      <SheetContent
        side="right"
        className={cn(
          "w-[60vw] max-w-none overflow-y-auto bg-gray-200 p-0 ps-4 pt-8 sm:max-w-none",
          className,
        )}
      >
        <div className="flex flex-wrap overflow-y-auto">
          <PolaroidShell
            topSlot={
              <div className="relative h-28 overflow-hidden rounded-lg">
                <div className="pointer-events-none absolute inset-y-0 left-0 w-14 bg-gradient-to-r from-neutral-50" />
                <div className="pointer-events-none absolute inset-y-0 right-0 w-14 bg-gradient-to-l from-neutral-50" />
                <DanmakuLane items={lanes[0]} duration={22} />
                <DanmakuLane items={lanes[1]} duration={28} reverse />
                <DanmakuLane items={lanes[2]} duration={34} />
              </div>
            }
            bottomSlot={
              <div className="max-h-[56vh] overflow-y-auto font-light">
                {data.briefDescriptions[0]}
              </div>
            }
          />

          <PolaroidShell
            topSlot={
              <div className="relative h-28 overflow-hidden rounded-lg">
                <div className="pointer-events-none absolute inset-y-0 left-0 w-14 bg-gradient-to-r from-neutral-50" />
                <div className="pointer-events-none absolute inset-y-0 right-0 w-14 bg-gradient-to-l from-neutral-50" />
                <DanmakuLane items={lanes[0]} duration={22} />
                <DanmakuLane items={lanes[1]} duration={28} reverse />
                <DanmakuLane items={lanes[2]} duration={34} />
              </div>
            }
            bottomSlot={
              <div className="max-h-[56vh] overflow-y-auto font-light">
                {data.briefDescriptions[1]}
              </div>
            }
          />

          {data.detailedDescriptions.map((description, i) => (
            <PolaroidShell
              key={i}
              topSlot={
                <div className="relative h-full overflow-hidden rounded-lg">
                  <p className="px-3 py-4 text-xl font-light italic">
                    {description}
                  </p>
                </div>
              }
              bottomSlot={
                <div className="max-h-[56vh] overflow-y-auto font-light">
                  {data.briefDescriptions[0]}
                </div>
              }
            />
          ))}

          <style>{`
          @keyframes marquee { 0% { transform: translateX(0) } 100% { transform: translateX(-50%) } }
          @keyframes marquee-rev { 0% { transform: translateX(-50%) } 100% { transform: translateX(0) } }
        `}</style>
        </div>
      </SheetContent>
    </Sheet>
  )
}

/* -------------------- 子组件：拍立得骨架 -------------------- */
function PolaroidShell({
  topSlot,
  bottomSlot,
}: {
  topSlot?: React.ReactNode
  bottomSlot?: React.ReactNode
}) {
  return (
    <div
      className={cn(
        "m-4 w-[330px] rounded-md border border-neutral-200/80 bg-white shadow-xl",
        "relative overflow-hidden",
        "[--polaroid-bg:theme(colors.white)] [--polaroid-border:theme(colors.neutral.200)]",
      )}
      style={{
        aspectRatio: `${0.7}`,
        background: "var(--polaroid-bg)",
      }}
    >
      {/* 成像区 */}
      <div
        className={
          "absolute inset-0 [--bottom:24.132%] [--side:5.383%] [--top:4.429%]"
        }
      >
        <div className="absolute top-[var(--top)] right-[var(--side)] bottom-[var(--bottom)] left-[var(--side)] overflow-hidden rounded-sm bg-neutral-100">
          {/* 顶部槽位（弹幕标签） */}
          <div className="absolute top-0 right-0 left-0">{topSlot}</div>
          {/* 图像区域占位 */}
          <div className="h-full w-full" />
        </div>
      </div>

      <div className="absolute inset-x-0 bottom-8 flex h-16 items-center px-6 text-gray-600">
        {bottomSlot}
      </div>

      {/* 相纸底部留白（手写区） */}
      <div className="absolute inset-x-0 bottom-0 flex h-[var(--bottom)] items-center border-t border-[var(--polaroid-border)] bg-white/95 px-4">
        <span className="text-[11px] tracking-wide text-neutral-500">
          Gensmo
        </span>
      </div>

      {/* 下方滚动描述列表容器 */}
      {/* <section className="absolute right-0 bottom-0 left-0 mx-4 mb-6 overflow-hidden rounded-lg border border-neutral-200/80">
        
      </section> */}
    </div>
  )
}

/* -------------------- 子组件：弹幕轨道 -------------------- */
function DanmakuLane({
  items,
  duration = 24,
  reverse = false,
}: {
  items: string[]
  duration?: number
  reverse?: boolean
}) {
  const animClass = reverse
    ? "[animation:marquee-rev_linear_infinite]"
    : "[animation:marquee_linear_infinite]"
  const style: React.CSSProperties = { animationDuration: `${duration}s` }
  return (
    <div className="relative h-9">
      <div
        className={cn(
          "absolute right-0 left-0 flex min-w-[200%] items-center gap-3 px-4 py-1 whitespace-nowrap",
          animClass,
        )}
        style={style}
      >
        <TagRow items={items} />
        <TagRow items={items} aria-hidden />
      </div>
    </div>
  )
}

function TagRow({
  items,
  ...rest
}: { items: string[] } & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className="flex items-center gap-3" {...rest}>
      {items.map((tag, i) => (
        <span
          key={`${tag}-${i}`}
          className="inline-flex items-center rounded-full border border-neutral-200/80 px-3 py-1 text-xs font-medium text-neutral-700 shadow-sm"
        >
          {tag}
        </span>
      ))}
    </div>
  )
}

/* -------------------- 子组件：粘性标题 -------------------- */
function StickyHeader({ title }: { title: string }) {
  return (
    <div className="sticky top-0 z-10 border-b border-neutral-200/80 bg-white/80 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <h3 className="px-4 py-2 text-xs font-semibold tracking-wider text-neutral-600">
        {title}
      </h3>
    </div>
  )
}
